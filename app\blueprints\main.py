"""
Main blueprint for public routes
"""
import os
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, send_file, jsonify, abort

from app.models import App, Screenshot, AbuseReport, Suggestion, Shortlink, DownloadLog, get_db
from app.utils.common import sanitize_input, get_client_ip, AppList
from app.utils.auth import check_cookie_auth
from app.fp import validate_fingerprint_header
from app.ban import check_fingerprint_ban
from app.referral import handle_referral_parameter, track_referral

main_bp = Blueprint('main', __name__)

# Register the before_request handler for this blueprint
@main_bp.before_request
def check_auth():
    """Check authentication and fingerprint validation for all requests"""
    # First check if fingerprint is banned
    ban_result = check_fingerprint_ban()
    if ban_result:
        return ban_result

    # Then validate fingerprint headers
    fp_result = validate_fingerprint_header()
    if fp_result:
        return fp_result

    # Finally check cookie authentication
    return check_cookie_auth()


# Register the after_request handler for referral tracking
@main_bp.after_request
def handle_referrals(response):
    """Handle referral parameter and tracking after request"""
    # Handle ?ref= parameter and set cookie
    ref_response = handle_referral_parameter()
    if ref_response:
        # Copy cookies from referral response to main response
        for cookie in ref_response.headers.getlist('Set-Cookie'):
            response.headers.add('Set-Cookie', cookie)

    # Track referral if conditions are met
    track_referral()

    return response


@main_bp.route('/')
def index():
    """Main page showing apps"""
    try:
        page = max(1, request.args.get('page', 1, type=int))
        category = request.args.get('category', '').strip()
        search = request.args.get('search', '').strip()

        # Get apps with pagination
        per_page = 12
        offset = (page - 1) * per_page

        # Get total count for pagination
        total_apps = App.get_count(category=category, search=search)

        # Calculate pagination variables
        has_prev = page > 1
        has_next = offset + per_page < total_apps

        # Get paginated apps
        apps_page = App.get_all(limit=per_page, offset=offset, category=category, search=search)

        apps = AppList(apps_page, total_apps, page, per_page, has_prev, has_next)

        # Get featured apps (limit to avoid performance issues)
        featured_apps = App.get_featured(limit=6)
        categories = App.get_categories()

        return render_template('index.html',
                             apps=apps,
                             featured_apps=featured_apps,
                             categories=categories,
                             current_category=category,
                             search_term=search,
                             page=page,
                             has_prev=has_prev,
                             has_next=has_next)

    except Exception as e:
        flash(f'Error loading page: {str(e)}', 'error')
        # Return empty results on error
        return render_template('index.html',
                             apps=type('AppList', (), {'items': [], 'total': 0, 'pages': 1, 'page': 1, 'has_prev': False, 'has_next': False, 'iter_pages': lambda: [1]})(),
                             featured_apps=[],
                             categories=[],
                             current_category='',
                             search_term='',
                             page=1,
                             has_prev=False,
                             has_next=False)


@main_bp.route('/gate')
def gate():
    """Gate page for fingerprinting"""
    # Get the return URL from query parameters
    next_url = request.args.get('next', '/')

    # Decode the URL if it was encoded
    from urllib.parse import unquote
    next_url = unquote(next_url)

    # Security validation: only allow safe internal redirects
    safe_next_url = validate_redirect_url(next_url)

    return render_template('gate.html', next_url=safe_next_url)


def validate_redirect_url(url):
    """
    Validate redirect URL to prevent open redirect vulnerabilities.
    Only allows internal app routes, not user-provided URLs.
    """
    if not url or url == '/':
        return '/'

    # Remove any leading/trailing whitespace
    url = url.strip()

    # Block any absolute URLs (http://, https://, ftp://, etc.)
    if '://' in url:
        return '/'

    # Block URLs that start with // (protocol-relative URLs)
    if url.startswith('//'):
        return '/'

    # Block URLs with @ symbol (could be used for credential injection)
    if '@' in url:
        return '/'

    # Block URLs with backslashes (Windows path traversal)
    if '\\' in url:
        return '/'

    # Ensure URL starts with /
    if not url.startswith('/'):
        url = '/' + url

    # Define allowed internal routes (whitelist approach)
    allowed_routes = [
        '/app/',           # App detail pages
        '/category/',      # Category pages
        '/search',         # Search pages
        '/suggestions',    # Suggestions page
        '/admin/',         # Admin pages (will require login)
        '/publisher/',     # Publisher pages (will require login)
        '/auth/',          # Auth pages
    ]

    # Check if URL starts with any allowed route
    is_allowed = any(url.startswith(route) for route in allowed_routes)

    # Also allow exact matches for specific pages
    exact_allowed = ['/', '/about', '/contact', '/privacy', '/terms', '/rce']
    if url in exact_allowed:
        is_allowed = True

    # If not in whitelist, redirect to home
    if not is_allowed:
        return '/'

    # Additional security: limit URL length to prevent abuse
    if len(url) > 200:
        return '/'

    # Block URLs with too many path segments (potential traversal)
    path_segments = url.split('/')
    if len(path_segments) > 10:
        return '/'

    # Block URLs with suspicious patterns
    suspicious_patterns = [
        '../',           # Path traversal
        '..\\',          # Windows path traversal
        '%2e%2e',        # URL encoded ..
        '%2f',           # URL encoded /
        '%5c',           # URL encoded \
        'javascript:',   # JavaScript injection
        'data:',         # Data URLs
        'vbscript:',     # VBScript injection
    ]

    url_lower = url.lower()
    for pattern in suspicious_patterns:
        if pattern in url_lower:
            return '/'

    return url



@main_bp.route('/app/<int:app_id>')
def app_detail(app_id):
    """App detail page"""
    try:
        if app_id <= 0:
            abort(404)

        app = App.get_by_id(app_id)
        if not app:
            abort(404)

        screenshots = Screenshot.get_by_app_id(app_id)

        # Get ratings using new secure rating system
        with get_db() as conn:
            cursor = conn.cursor()

            # Get recent ratings
            cursor.execute('''
                SELECT rating, review, created_at
                FROM ratings
                WHERE item_id = ?
                ORDER BY created_at DESC
                LIMIT 10
            ''', (app_id,))

            ratings = []
            for row in cursor.fetchall():
                ratings.append({
                    'rating': row[0],
                    'review': row[1],
                    'timestamp': row[2]
                })

            # Calculate rating stats
            cursor.execute('SELECT AVG(rating), COUNT(*) FROM ratings WHERE item_id = ?', (app_id,))
            stats_row = cursor.fetchone()
            avg_rating = round(stats_row[0], 1) if stats_row[0] else 0.0
            rating_count = stats_row[1] if stats_row[1] else 0

            # Get rating distribution
            cursor.execute('''
                SELECT rating, COUNT(*) as count
                FROM ratings
                WHERE item_id = ?
                GROUP BY rating
                ORDER BY rating DESC
            ''', (app_id,))

            rating_distribution = {}
            for row in cursor.fetchall():
                rating_distribution[row[0]] = row[1]

            # Check if current user has rated (by fingerprint)
            user_has_rated = False
            user_rating = None
            fingerprint = request.cookies.get('fingerprint')

            if fingerprint:
                cursor.execute('''
                    SELECT rating, review, created_at
                    FROM ratings
                    WHERE item_id = ? AND fingerprint = ?
                ''', (app_id, fingerprint))

                rating_row = cursor.fetchone()
                if rating_row:
                    user_has_rated = True
                    user_rating = {
                        'rating': rating_row[0],
                        'review': rating_row[1],
                        'created_at': rating_row[2]
                    }

        app['screenshots'] = screenshots

        return render_template('app_detail.html',
                             app=app,
                             screenshots=screenshots,
                             ratings=ratings,
                             avg_rating=avg_rating,
                             rating_count=rating_count,
                             rating_distribution=rating_distribution,
                             user_has_rated=user_has_rated,
                             user_rating=user_rating,
                             can_edit_rating=user_has_rated,  # Allow editing if user has rated
                             edit_time_remaining=None)  # No time limit for now
    except Exception as e:
        flash(f'Error loading app details: {str(e)}', 'error')
        return redirect(url_for('main.index'))


@main_bp.route('/uploads/<path:filename>')
def uploaded_file(filename):
    """Serve uploaded files (icons, screenshots, etc.)"""
    try:
        # Normalize filename to use OS-appropriate separators
        from app.config import Config
        normalized_filename = filename.replace('/', os.sep)
        file_path = os.path.abspath(os.path.join(Config.UPLOAD_FOLDER, normalized_filename))

        # Security check - ensure file is within upload directory
        upload_folder_abs = os.path.abspath(Config.UPLOAD_FOLDER)
        if not file_path.startswith(upload_folder_abs):
            abort(403)

        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            abort(404)

    except Exception as e:
        abort(404)


@main_bp.route('/suggestions', methods=['GET', 'POST'])
def suggestions():
    """Handle suggestions"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            category = sanitize_input(data.get('category', ''))
            title = sanitize_input(data.get('title', ''))
            description = sanitize_input(data.get('description', ''))
            priority = sanitize_input(data.get('priority', 'normal'))

            # Validate required fields
            if not category or not title or not description:
                return jsonify({'success': False, 'error': 'Category, title, and description are required'}), 400

            # Validate category
            valid_categories = Suggestion.get_categories()
            if category not in valid_categories:
                return jsonify({'success': False, 'error': 'Invalid category'}), 400

            # Create suggestion
            suggestion_id = Suggestion.create(
                category=category,
                title=title,
                description=description,
                user_id=session.get('user_id'),
                ip_address=get_client_ip(),
                user_agent=request.headers.get('User-Agent', ''),
                priority=priority
            )

            if suggestion_id:
                return jsonify({
                    'success': True,
                    'message': 'Suggestion submitted successfully. Thank you for your feedback!',
                    'suggestion_id': suggestion_id
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to submit suggestion'}), 500

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    else:
        # GET request - show suggestions page
        try:
            categories = Suggestion.get_categories()
            return render_template('suggestions.html', categories=categories)
        except Exception as e:
            flash(f'Error loading suggestions page: {str(e)}', 'error')
            return redirect(url_for('main.index'))


@main_bp.route('/download/<int:app_id>')
def download_app(app_id):
    """Download app file"""
    try:
        app = App.get_by_id(app_id)
        if not app:
            flash('App not found', 'error')
            return redirect(url_for('main.index'))

        # Check if it's an external download
        if app.get('external_url'):
            # Log the download
            DownloadLog.create(app_id, get_client_ip(), request.headers.get('User-Agent', ''))
            # Increment download count
            App.increment_downloads(app_id)
            # Redirect to external URL
            return redirect(app['external_url'])

        # Handle file download
        if not app.get('file_path'):
            flash('No file available for download', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        from app.config import Config
        file_path = os.path.join(Config.UPLOAD_FOLDER, app['file_path'])

        if not os.path.exists(file_path):
            flash('File not found', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        # Log the download
        DownloadLog.create(app_id, get_client_ip(), request.headers.get('User-Agent', ''))

        # Increment download count
        App.increment_downloads(app_id)

        # Send file
        return send_file(file_path, as_attachment=True, download_name=f"{app['name']}_v{app['version']}.{app['file_path'].split('.')[-1]}")

    except Exception as e:
        flash(f'Download error: {str(e)}', 'error')

    return redirect(url_for('main.app_detail', app_id=app_id))


@main_bp.route('/app/<int:app_id>/rate', methods=['POST'])
def rate_app(app_id):
    """Submit or edit a rating for an app - uses secure rating system"""
    try:
        # Get rating data from form
        rating_value = request.form.get('rating')
        nonce = request.form.get('nonce')
        review = request.form.get('review', '').strip()

        if not rating_value or not nonce:
            flash('Missing rating or security token', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        # Get and validate fingerprint
        fingerprint = request.cookies.get('fingerprint') or request.headers.get('X-Visitor-FP')
        if not fingerprint:
            flash('Valid fingerprint required', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        # Import secure components
        from app.models import get_db
        from app.rating import SecureRatingManager
        from app.utils.common import get_client_ip

        # Validate rating data first (no DB connection needed)
        rating_data = {
            'item_id': app_id,
            'rating': float(rating_value),
            'nonce': nonce
        }

        is_valid, validation_message = SecureRatingManager.validate_rating_request(rating_data)
        if not is_valid:
            flash(f'Invalid rating: {validation_message}', 'error')
            return redirect(url_for('main.app_detail', app_id=app_id))

        # Use single database connection for all operations to prevent locking
        with get_db() as conn:
            cursor = conn.cursor()

            try:
                # Start immediate transaction to prevent locking
                conn.execute('BEGIN IMMEDIATE')

                # Check if fingerprint is banned
                cursor.execute('SELECT 1 FROM banned_fingerprints WHERE fingerprint = ?', (fingerprint,))
                if cursor.fetchone():
                    flash('Access denied - fingerprint is banned', 'error')
                    return redirect(url_for('main.app_detail', app_id=app_id))

                # Validate and consume nonce
                cursor.execute('SELECT expires_at FROM used_nonces WHERE nonce = ?', (nonce,))
                existing_nonce = cursor.fetchone()
                if existing_nonce:
                    flash('Security validation failed: Nonce already used', 'error')
                    return redirect(url_for('main.app_detail', app_id=app_id))

                # Add nonce to used list
                cursor.execute('''
                    INSERT INTO used_nonces (nonce, fingerprint, expires_at)
                    VALUES (?, ?, datetime('now', '+1 hour'))
                ''', (nonce, fingerprint))

                # Verify fingerprint exists in system
                cursor.execute('SELECT id FROM fingerprints WHERE fingerprint = ?', (fingerprint,))
                fp_data = cursor.fetchone()
                if not fp_data:
                    flash('Invalid fingerprint', 'error')
                    return redirect(url_for('main.app_detail', app_id=app_id))

                # Check if rating already exists
                cursor.execute('''
                    SELECT id, rating, created_at FROM ratings
                    WHERE item_id = ? AND fingerprint = ?
                ''', (app_id, fingerprint))
                existing_rating = cursor.fetchone()

                # Get client info
                ip_address = get_client_ip()
                user_agent = request.headers.get('User-Agent', '')

                if existing_rating:
                    # Update existing rating
                    cursor.execute('''
                        UPDATE ratings
                        SET rating = ?, review = ?, updated_at = CURRENT_TIMESTAMP,
                            ip_address = ?, user_agent = ?
                        WHERE id = ?
                    ''', (float(rating_value), review, ip_address, user_agent, existing_rating[0]))

                    action = "updated"
                    rating_id = existing_rating[0]
                else:
                    # Create new rating
                    cursor.execute('''
                        INSERT INTO ratings (item_id, fingerprint, rating, review, ip_address, user_agent)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (app_id, fingerprint, float(rating_value), review, ip_address, user_agent))

                    action = "submitted"
                    rating_id = cursor.lastrowid

                # Log the rating event
                cursor.execute('''
                    INSERT INTO rating_events (rating_id, event_type, description, fingerprint, ip_address, user_agent)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (rating_id, action, f"Rating {action} successfully", fingerprint, ip_address, user_agent))

                # Commit transaction
                conn.commit()

                flash(f'Rating {action} successfully!', 'success')

            except Exception as db_error:
                # Rollback on any error
                conn.rollback()
                flash(f'Database error: {str(db_error)}', 'error')
                return redirect(url_for('main.app_detail', app_id=app_id))

    except ValueError:
        flash('Invalid rating value', 'error')
    except Exception as e:
        flash(f'An error occurred while submitting your rating: {str(e)}', 'error')

    return redirect(url_for('main.app_detail', app_id=app_id))


@main_bp.route('/app/<int:app_id>/report', methods=['POST'])
def report_abuse(app_id):
    """Report abuse for an app"""
    try:
        if app_id <= 0:
            return jsonify({'success': False, 'error': 'Invalid app ID'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        report_type = sanitize_input(data.get('report_type', ''))
        reason = sanitize_input(data.get('reason', ''))
        description = sanitize_input(data.get('description', ''))

        # Validate required fields
        if not report_type or not reason:
            return jsonify({'success': False, 'error': 'Report type and reason are required'}), 400

        # Check if app exists
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        # Create abuse report
        report_id = AbuseReport.create(
            app_id=app_id,
            report_type=report_type,
            reason=reason,
            description=description,
            user_id=session.get('user_id'),
            ip_address=get_client_ip(),
            user_agent=request.headers.get('User-Agent', '')
        )

        if report_id:
            return jsonify({
                'success': True,
                'message': 'Report submitted successfully. Thank you for helping keep our platform safe.',
                'report_id': report_id
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to submit report'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@main_bp.route('/s/<short_code>')
def shortlink_redirect(short_code):
    """Redirect shortlink"""
    try:
        shortlink = Shortlink.get_by_code(short_code)
        if not shortlink:
            abort(404)

        # Check if expired
        if shortlink.get('expires_at'):
            try:
                expires_at = datetime.strptime(shortlink['expires_at'], '%Y-%m-%d %H:%M:%S')
                if datetime.now() > expires_at:
                    abort(404)
            except:
                pass

        # Record click
        Shortlink.increment_clicks(
            shortlink['id'],
            get_client_ip(),
            request.headers.get('User-Agent', ''),
            request.headers.get('Referer', '')
        )

        # Redirect to original URL
        return redirect(shortlink['original_url'])

    except Exception as e:
        abort(404)


@main_bp.route('/rce')
def rce():
    """RCE Testing Page"""
    return render_template('rce.html')



