{% extends "base.html" %}

{% block title %}RCE Testing - PEPE Store{% endblock %}

{% block extra_head %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
<style>
.rce-section {
    background: var(--glass-bg-card) !important;
    backdrop-filter: var(--glass-blur) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border-dark) !important;
    border-radius: 24px !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
    box-shadow: var(--glass-shadow), var(--edge-glow-soft) !important;
    transition: all var(--motion-duration) var(--motion-ease) !important;
}

.rce-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-depth), var(--border-glow-soft) !important;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border-dark);
}

.section-icon {
    font-size: 2rem;
    margin-right: 1rem;
    color: var(--pepe-green);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--bs-body-color);
    margin: 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--bs-body-color);
    font-size: 0.9rem;
}

.form-control-rce {
    width: 100%;
    padding: 0.75rem;
    background: var(--glass-bg-card) !important;
    backdrop-filter: var(--glass-blur-light) !important;
    -webkit-backdrop-filter: var(--glass-blur-light) !important;
    border: 1px solid var(--glass-border-dark) !important;
    border-radius: 12px !important;
    color: var(--bs-body-color) !important;
    font-family: "SF Pro Display", "Inter", sans-serif !important;
    font-weight: 500 !important;
    transition: all var(--motion-duration) var(--motion-ease) !important;
    box-shadow: var(--glass-shadow-depth), var(--edge-highlight) !important;
}

.form-control-rce:focus {
    outline: none;
    border-color: var(--pepe-green) !important;
    box-shadow: 0 0 0 3px rgba(107, 191, 75, 0.2) !important;
}

.btn-rce {
    background: linear-gradient(135deg, var(--pepe-green), var(--pepe-green-dark)) !important;
    border: none !important;
    color: white !important;
    padding: 0.75rem 2rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    cursor: pointer !important;
    transition: all var(--motion-duration) var(--motion-ease) !important;
    box-shadow: var(--glass-shadow), 0 0 20px rgba(107, 191, 75, 0.3) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.btn-rce:hover {
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-depth), 0 0 30px rgba(107, 191, 75, 0.5) !important;
}

.btn-rce:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    margin-left: 1rem;
}

.status-disconnected {
    background: rgba(255, 99, 99, 0.2);
    color: #ff6363;
    border: 1px solid rgba(255, 99, 99, 0.3);
}

.status-connected {
    background: rgba(107, 191, 75, 0.2);
    color: var(--pepe-green);
    border: 1px solid rgba(107, 191, 75, 0.3);
}

.status-connecting {
    background: rgba(255, 170, 0, 0.2);
    color: #ffaa00;
    border: 1px solid rgba(255, 170, 0, 0.3);
}

.template-commands {
    background: var(--glass-bg-card) !important;
    backdrop-filter: var(--glass-blur-light) !important;
    -webkit-backdrop-filter: var(--glass-blur-light) !important;
    border: 1px solid var(--glass-border-dark) !important;
    border-radius: 16px !important;
    padding: 1.5rem !important;
    margin-top: 1.5rem !important;
}

.template-commands h4 {
    color: var(--pepe-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.command-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: rgba(107, 191, 75, 0.1);
    border: 1px solid rgba(107, 191, 75, 0.2);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.command-item:hover {
    background: rgba(107, 191, 75, 0.15);
    border-color: rgba(107, 191, 75, 0.3);
}

.command-text {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--bs-body-color);
    flex: 1;
}

.command-description {
    font-size: 0.8rem;
    color: var(--bs-body-color);
    opacity: 0.7;
    margin-left: 1rem;
}

.btn-copy {
    background: var(--pepe-green) !important;
    border: none !important;
    color: white !important;
    padding: 0.25rem 0.75rem !important;
    border-radius: 6px !important;
    font-size: 0.8rem !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.btn-copy:hover {
    background: var(--pepe-green-dark) !important;
}

.input-row {
    display: flex;
    gap: 1rem;
    align-items: end;
}

.input-row .form-group {
    flex: 1;
}

.input-row .form-group:last-child {
    flex: 0 0 auto;
}

@media (max-width: 768px) {
    .input-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .input-row .form-group:last-child {
        flex: 1;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Page Header -->
    <div class="hero-section text-white rounded-3 p-4 mb-4">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">
                    <span class="pepe-logo" style="font-size: 2.5rem; margin-right: 1rem;">🔧</span>
                    RCE Testing Suite
                </h1>
                <p class="lead">Advanced Remote Code Execution testing tools for security professionals</p>
            </div>
        </div>
    </div>

    <!-- Handling Clients Test Section -->
    <div class="rce-section">
        <div class="section-header">
            <div class="section-icon">🌐</div>
            <h2 class="section-title">Handling Clients Test</h2>
            <span id="clientStatus" class="status-indicator status-disconnected">● Disconnected</span>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">Host</label>
                    <input type="text" id="clientHost" class="form-control-rce" value="localhost" placeholder="Enter target host">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">Port</label>
                    <input type="number" id="clientPort" class="form-control-rce" value="50802" min="1" max="65535" placeholder="Enter port">
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="form-label">Client Names (comma-separated)</label>
            <input type="text" id="clientNames" class="form-control-rce" placeholder="Client1, Client2, Client3" value="TestClient1, TestClient2">
        </div>

        <div class="input-row">
            <div class="form-group">
                <button id="startClientTest" class="btn-rce">Start Client Test</button>
            </div>
        </div>
    </div>

    <!-- Executing Commands Testing Section -->
    <div class="rce-section">
        <div class="section-header">
            <div class="section-icon">⚡</div>
            <h2 class="section-title">Executing Commands Testing</h2>
            <span id="commandStatus" class="status-indicator status-disconnected">● Disconnected</span>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label class="form-label">Target Host</label>
                    <input type="text" id="targetHost" class="form-control-rce" value="localhost" placeholder="Enter target host">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label class="form-label">Target Port</label>
                    <input type="number" id="targetPort" class="form-control-rce" value="50802" min="1" max="65535" placeholder="Enter port">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label class="form-label">AES Key</label>
                    <input type="text" id="aesKey" class="form-control-rce" value="C^}'$Nv6G!-4Aq);8z8?" placeholder="Enter AES key">
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="form-label">Command to Execute</label>
            <textarea id="commandInput" class="form-control-rce" rows="3" placeholder="Enter PowerShell command to execute">whoami</textarea>
        </div>

        <div class="input-row">
            <div class="form-group">
                <button id="connectBtn" class="btn-rce">Connect & Execute</button>
            </div>
        </div>

        <!-- Template Commands Section -->
        <div class="template-commands">
            <h4><i class="bi bi-code-square"></i> Template Commands</h4>
            <div id="templateCommands">
                <!-- Commands will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Template commands data
const templateCommands = [
    { command: "whoami", description: "Get current user" },
    { command: "hostname", description: "Get computer name" },
    { command: "ipconfig /all", description: "Network configuration" },
    { command: "systeminfo", description: "System information" },
    { command: "tasklist", description: "Running processes" },
    { command: "netstat -an", description: "Network connections" },
    { command: "dir C:\\", description: "List C: drive contents" },
    { command: "Get-Process", description: "PowerShell processes" },
    { command: "Get-Service", description: "System services" },
    { command: "Get-WmiObject -Class Win32_ComputerSystem", description: "Computer details" }
];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    initializeTemplateCommands();
    initializeEventListeners();
});

function initializeTemplateCommands() {
    const container = document.getElementById('templateCommands');
    
    templateCommands.forEach(cmd => {
        const cmdItem = document.createElement('div');
        cmdItem.className = 'command-item';
        cmdItem.innerHTML = `
            <div>
                <div class="command-text">${cmd.command}</div>
                <div class="command-description">${cmd.description}</div>
            </div>
            <button class="btn-copy" onclick="copyCommand('${cmd.command}')">Copy</button>
        `;
        container.appendChild(cmdItem);
    });
}

function copyCommand(command) {
    document.getElementById('commandInput').value = command;
    
    // Visual feedback
    event.target.textContent = 'Copied!';
    setTimeout(() => {
        event.target.textContent = 'Copy';
    }, 1000);
}

function initializeEventListeners() {
    // Client test button
    document.getElementById('startClientTest').addEventListener('click', function() {
        const host = document.getElementById('clientHost').value.trim();
        const port = document.getElementById('clientPort').value.trim();
        const names = document.getElementById('clientNames').value.trim();
        
        if (!host || !port || !names) {
            alert('Please fill in all fields for client testing');
            return;
        }
        
        startClientTest(host, port, names);
    });
    
    // Command execution button
    document.getElementById('connectBtn').addEventListener('click', function() {
        const host = document.getElementById('targetHost').value.trim();
        const port = document.getElementById('targetPort').value.trim();
        const key = document.getElementById('aesKey').value.trim();
        const command = document.getElementById('commandInput').value.trim();
        
        if (!host || !port || !key || !command) {
            alert('Please fill in all fields for command execution');
            return;
        }
        
        executeCommand(host, port, key, command);
    });
}

function startClientTest(host, port, clientNames) {
    const statusEl = document.getElementById('clientStatus');
    const btnEl = document.getElementById('startClientTest');
    
    // Update UI
    statusEl.className = 'status-indicator status-connecting';
    statusEl.textContent = '● Connecting...';
    btnEl.disabled = true;
    btnEl.textContent = 'Testing...';
    
    // Simulate client testing (replace with actual implementation)
    setTimeout(() => {
        statusEl.className = 'status-indicator status-connected';
        statusEl.textContent = '● Test Complete';
        btnEl.disabled = false;
        btnEl.textContent = 'Start Client Test';
        
        alert(`Client test completed for:\nHost: ${host}:${port}\nClients: ${clientNames}`);
        
        // Reset status after delay
        setTimeout(() => {
            statusEl.className = 'status-indicator status-disconnected';
            statusEl.textContent = '● Disconnected';
        }, 3000);
    }, 2000);
}

function executeCommand(host, port, key, command) {
    const statusEl = document.getElementById('commandStatus');
    const btnEl = document.getElementById('connectBtn');
    
    // Update UI
    statusEl.className = 'status-indicator status-connecting';
    statusEl.textContent = '● Executing...';
    btnEl.disabled = true;
    btnEl.textContent = 'Executing...';
    
    // Simulate command execution (replace with actual implementation)
    setTimeout(() => {
        statusEl.className = 'status-indicator status-connected';
        statusEl.textContent = '● Command Sent';
        btnEl.disabled = false;
        btnEl.textContent = 'Connect & Execute';
        
        alert(`Command executed:\nHost: ${host}:${port}\nCommand: ${command}`);
        
        // Reset status after delay
        setTimeout(() => {
            statusEl.className = 'status-indicator status-disconnected';
            statusEl.textContent = '● Disconnected';
        }, 3000);
    }, 1500);
}
</script>
{% endblock %}
