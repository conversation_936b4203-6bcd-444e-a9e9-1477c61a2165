"""
Authentication blueprint for login/logout routes
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from app.models import User, LoginLog, BlockedIP
from app.utils.common import sanitize_input, get_client_ip, verify_recaptcha, check_ip_blocked, handle_failed_login, log_security_event

auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login page"""
    if request.method == 'POST':
        try:
            client_ip = get_client_ip()

            # Check if IP is blocked
            if check_ip_blocked(client_ip):
                # Log blocked IP access attempt
                log_security_event(
                    event_type='BLOCKED_IP_ACCESS',
                    username='Unknown',
                    ip_address=client_ip,
                    details='Blocked IP attempted to access login page',
                    success=False
                )
                flash('Your IP address has been temporarily blocked due to too many failed login attempts. Please try again later.', 'error')
                return render_template('auth/login.html')

            username = sanitize_input(request.form.get('username'))
            password = request.form.get('password')
            recaptcha_response = request.form.get('g-recaptcha-response')

            # Validate input
            if not username or not password:
                flash('Username and password are required', 'error')
                return render_template('auth/login.html')

            # Verify reCAPTCHA
            if not verify_recaptcha(recaptcha_response):
                # Log reCAPTCHA failure to database
                LoginLog.create(
                    user_id=None,
                    username=username or 'Unknown',
                    success=False,
                    ip_address=client_ip,
                    user_agent=request.headers.get('User-Agent', ''),
                    failure_reason='reCAPTCHA verification failed'
                )

                # Log reCAPTCHA failure to security log
                log_security_event(
                    event_type='CAPTCHA_FAILURE',
                    username=username or 'Unknown',
                    ip_address=client_ip,
                    details='reCAPTCHA verification failed',
                    success=False
                )

                flash('Please complete the reCAPTCHA verification', 'error')
                return render_template('auth/login.html')

            user = User.get_by_username(username)
            if user and User.check_password(user, password) and user.get('is_active'):
                # Update last login
                User.update_last_login(user['id'])

                # Set session
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['role'] = user['role']
                session.permanent = True

                # Log successful login to database
                LoginLog.create(
                    user_id=user['id'],
                    username=user['username'],
                    success=True,
                    ip_address=client_ip,
                    user_agent=request.headers.get('User-Agent', '')
                )

                # Log successful login to security log
                log_security_event(
                    event_type='LOGIN_ATTEMPT',
                    username=user['username'],
                    ip_address=client_ip,
                    details=f"role:{user['role']}",
                    success=True
                )

                # Reset failed login attempts for this IP on successful login
                BlockedIP.reset_failed_attempts(client_ip)

                flash(f'Welcome back, {user["username"]}!', 'success')

                # Redirect based on role
                if User.is_admin(user):
                    return redirect(url_for('admin.dashboard'))
                else:
                    return redirect(url_for('publisher.dashboard'))
            else:
                # Log failed login to database FIRST
                LoginLog.create(
                    user_id=None,
                    username=username or 'Unknown',
                    success=False,
                    ip_address=client_ip,
                    user_agent=request.headers.get('User-Agent', ''),
                    failure_reason='Invalid credentials or inactive account'
                )

                # Log failed login to security log
                log_security_event(
                    event_type='LOGIN_ATTEMPT',
                    username=username or 'Unknown',
                    ip_address=client_ip,
                    details='Invalid credentials or inactive account',
                    success=False
                )

                # Check failed attempts and potentially block IP
                from app.config import Config

                # Get failed attempts in last 30 minutes (including the one we just logged)
                failed_attempts = BlockedIP.get_failed_attempts(client_ip, 30)

                print(f"DEBUG: IP {client_ip} has {failed_attempts} failed attempts")  # Debug line

                if failed_attempts >= Config.MAX_LOGIN_ATTEMPTS:
                    # Block the IP immediately
                    block_success = BlockedIP.block_ip(
                        client_ip,
                        duration_minutes=Config.BLOCK_DURATION_MINUTES,
                        reason=f'Too many failed login attempts ({failed_attempts})'
                    )

                    # Log IP blocking to security log
                    log_security_event(
                        event_type='IP_BLOCKED',
                        username=username or 'Unknown',
                        ip_address=client_ip,
                        details=f'Blocked for {Config.BLOCK_DURATION_MINUTES} minutes after {failed_attempts} failed attempts',
                        success=block_success
                    )

                    print(f"DEBUG: Blocked IP {client_ip}: {block_success}")  # Debug line
                    flash(f'Your IP has been blocked due to {failed_attempts} failed login attempts. Please try again later.', 'error')
                else:
                    remaining_attempts = Config.MAX_LOGIN_ATTEMPTS - failed_attempts
                    if remaining_attempts <= 1:
                        flash(f'Invalid credentials. Warning: {remaining_attempts} attempt remaining before IP block.', 'warning')
                    else:
                        flash(f'Invalid credentials. {remaining_attempts} attempts remaining before IP block.', 'error')

        except Exception as e:
            flash(f'Login error: {str(e)}', 'error')

    return render_template('auth/login.html')


@auth_bp.route('/logout')
def logout():
    """User logout"""
    # Log logout event before clearing session
    username = session.get('username', 'Unknown')
    client_ip = get_client_ip()

    log_security_event(
        event_type='LOGOUT',
        username=username,
        ip_address=client_ip,
        details='User logged out',
        success=True
    )

    session.clear()
    flash('You have been logged out', 'info')
    return redirect(url_for('main.index'))

